import numpy as np
import torch
import smplx
import trimesh
import os

# 1. 加载 .npz 文件
file_path = 'data/intercap/sequences_canonical_rename/sub06_soccerball_001/human.npz'
# file_path = '"/data-local/dingbang/phys_hoi_recon/PHC/ACCAD/Male1Walking_c3d/Walk_B10_-_Walk_turn_left_45_stageii.npz"'
data = np.load(file_path, allow_pickle=True)

# 2. 读取第一帧

pose = data['poses']       # (156,)
betas = data['betas']          # (16,)
print(betas)

print("Pose shape:", betas.shape)
trans = data['trans']       # (3,)
gender = str(data['gender'].item())  # string: 'male' or 'female'
# pose = np.zeros_like(pose)
# trans = np.zeros_like(trans)
# 3. 加载 SMPL-X 模型（需要 SMPL-X 模型文件夹）
model_path = '/data-local/dingbang/phys_hoi_recon/InterAct/models'  # 替换为实际路径
smplx_model = smplx.create(
    model_path=model_path,
    model_type='smplx',
    gender=gender,
    num_betas=10,
    use_pca=False,
)
# smplx_model.num_betas = 16
# betas = betas[:10]
# 4. 构造输入张量
pose_tensor = torch.tensor(pose, dtype=torch.float32)     # (1, 156)
global_orient = pose_tensor[:, :3]
body_pose = pose_tensor[:, 3:66]
left_hand_pose = pose_tensor[:, 66:111]
right_hand_pose = pose_tensor[:, 111:156]
betas_tensor = torch.tensor(betas, dtype=torch.float32).unsqueeze(0).expand(pose_tensor.shape[0], -1)
jaw_pose = torch.zeros(pose_tensor.shape[0], 3)
leye_pose = torch.zeros(pose_tensor.shape[0], 3)
reye_pose = torch.zeros(pose_tensor.shape[0], 3)
expression = torch.zeros(pose_tensor.shape[0], 10)
transl = torch.tensor(trans, dtype=torch.float32)
print(pose_tensor.shape)
print(global_orient.shape,body_pose.shape,left_hand_pose.shape,right_hand_pose.shape,betas_tensor.shape,transl.shape)
#torch.Size([133, 3]) torch.Size([133, 63]) torch.Size([133, 45]) torch.Size([133, 45]) torch.Size([133, 16]) torch.Size([133, 3])

output = smplx_model(
    global_orient=global_orient,
    body_pose=body_pose,
    left_hand_pose=left_hand_pose,
    right_hand_pose=right_hand_pose,
    betas=betas_tensor,
    transl=transl,
    jaw_pose=jaw_pose,
    leye_pose=leye_pose,
    reye_pose=reye_pose,
    expression=expression,
)
vertices = output.vertices.detach().cpu().numpy()[0]
print(vertices[:,1].min(),'min1111')
faces = smplx_model.faces  # 模型自带 faces
joints = output.joints.detach().cpu().numpy()[0]

print(output.joints.shape)
# The absolute position of the root (pelvis) joint is at joints[0]
print("Pelvis position:", joints[0])  # This is the absolute position in world coordinates
print("trans:", trans[0])
print("diff:", trans[0] - joints[0])
# Mark the root position in the obj file with a small sphere
# Create spheres for all joints
spheres = []
points = trimesh.PointCloud(joints)
points.export("joint_markers.obj")
print("Root position marker saved as: root_marker.obj")
# 6. 导出为 .obj 文件
mesh = trimesh.Trimesh(vertices=vertices, faces=faces)
output_path = 'smplx_output2.obj'
mesh.export(output_path)
print(f"Mesh 已保存至: {output_path}")

# Extract joints positions from the SMPL-X output
joints = output.joints.detach().cpu().numpy()[0]  # shape: (N_joints, 3)
