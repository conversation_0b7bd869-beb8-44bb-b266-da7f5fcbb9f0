import os
import numpy as np
import torch
import smplx
from scipy.spatial.transform import Rotation as R
import trimesh

def axisangle_to_quat(aa):
    rot = R.from_rotvec(aa)
    quat_xyzw = rot.as_quat()
    quat_wxyz = np.concatenate([quat_xyzw[:, 3:4], quat_xyzw[:, :3]], axis=1)
    return quat_wxyz
def recover_mesh_from_hoi_data(hoi_data, smplx_model, object_points, object_faces):
    N = hoi_data.shape[0]

    # 1. 从 HOI data 还原 smplx 参数
    transl = hoi_data[:, 0:3]  # (N, 3)
    root_quat = hoi_data[:, 3:7]  # (N, 4) [w, x, y, z]
    root_rotvec = R.from_quat(root_quat[:, [0,1,2,3]]).as_rotvec()  # (N, 3)

    # 2. 从关节四元数恢复成 axis-angle
    quat_wxyz = hoi_data[:, 383:591].reshape(N * 52, 4)  # (N*52, 4)
    quat_xyzw = quat_wxyz[:, [0,1,3,2]]  # 转换为 [x,y,z,w] 顺序
    aa = R.from_quat(quat_xyzw).as_rotvec().reshape(N, 52 * 3)  # (N, 156)

    # 拆分各部分
    global_orient = aa[:, 0:3]         # (N, 3)
    body_pose = aa[:, 3:66]           # (N, 63)
    left_hand_pose = aa[:, 66:111]    # (N, 45)
    right_hand_pose = aa[:, 111:156]  # (N, 45)

    # 3. 构造 SMPLX 模型
    smplx_output = smplx_model(
        transl=torch.tensor(transl, dtype=torch.float32),
        global_orient=torch.tensor(global_orient, dtype=torch.float32),
        body_pose=torch.tensor(body_pose, dtype=torch.float32),
        left_hand_pose=torch.tensor(left_hand_pose, dtype=torch.float32),
        right_hand_pose=torch.tensor(right_hand_pose, dtype=torch.float32),
        expression=torch.zeros(N, 10),
        jaw_pose=torch.zeros(N, 3),
        leye_pose=torch.zeros(N, 3),
        reye_pose=torch.zeros(N, 3),
        betas=torch.zeros(1, 10).expand(N, -1),
        return_verts=True
    )

    smplx_verts = smplx_output.vertices.detach().cpu().numpy()  # (N, 10475, 3)

    # 4. 物体 mesh 位姿
    obj_trans = hoi_data[:, 318:321]        # (N, 3)
    obj_quat_wxyz = hoi_data[:, 321:325]    # (N, 4)
    obj_quat_xyzw = obj_quat_wxyz[:, [1,2,3,0]]
    obj_rot = R.from_quat(obj_quat_xyzw)

    obj_verts_all = []
    for i in range(N):
        obj_verts = obj_rot[i].apply(object_points) + obj_trans[i]
        obj_verts_all.append(obj_verts)

    object_verts = np.stack(obj_verts_all)  # (N, V_obj, 3)

    return smplx_verts, smplx_model.faces.astype(np.int32), object_verts, object_faces.astype(np.int32)

def save_combined_obj(smplx_verts, smplx_faces, object_verts, object_faces, save_path, frame_idx=0):
    """
    Save SMPLX + object mesh into one .obj file for a given frame
    """
    v_human = smplx_verts[frame_idx]
    v_obj = object_verts[frame_idx]

    # v_all = np.vstack([v_human, v_obj])
    f_human = smplx_faces
    # f_obj = object_faces + v_human.shape[0]  # shift

    # f_all = np.vstack([f_human, f_obj])
    v_all = v_human
    f_all = f_human

    with open(save_path, 'w') as f:
        for v in v_all:
            f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
        for face in f_all:
            f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")

    print(f"[✓] Saved mesh to {save_path}")

# ========== 使用示例 ==========

if __name__ == '__main__':
    # 加载 HOI 数据
    hoi_data = torch.load('//data-local/dingbang/phys_hoi_recon/InterMimic/InterAct/OMOMO/sub8_smallbox_002.pt')
    hoi_data = hoi_data.detach().numpy()

    # 加载物体 mesh 模板（zero pose）
    obj_mesh = trimesh.load('/data-local/dingbang/phys_hoi_recon/InterMimic/intermimic/data/assets/objects/objects/largetable/largetable.obj', process=False)
    object_points = obj_mesh.vertices.astype(np.float32)  # (V_obj, 3)
    object_faces = obj_mesh.faces.astype(np.int32)        # (F_obj, 3)

    # 加载 SMPLX 模型
    model_path = '/data-local/dingbang/phys_hoi_recon/InterAct/models'  # e.g., './models/smplx'
    smplx_model = smplx.create(
        model_path, model_type='smplx', gender='neutral',
        use_pca=False, ext='pkl'
    )

    # 还原 mesh
    smplx_verts, smplx_faces, object_verts, object_faces = recover_mesh_from_hoi_data(
        hoi_data, smplx_model, object_points, object_faces
    )

    # 导出某一帧为 OBJ
    save_combined_obj(
        smplx_verts, smplx_faces, object_verts, object_faces,
        save_path='output_frame_0012.obj',
        frame_idx=100  # 可改为你想导出的帧编号
    )

    # 如果你想导出所有帧，可以这样：
    # os.makedirs('output_objs', exist_ok=True)
    # for i in range(hoi_data.shape[0]):
    #     save_combined_obj(smplx_verts, smplx_faces, object_verts, object_faces,
    #                       save_path=f'output_objs/frame_{i:04d}.obj', frame_idx=i)
