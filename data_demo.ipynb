{"cells": [{"cell_type": "code", "execution_count": 1, "id": "843189d3", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "import os\n", "import trimesh\n", "\n", "MOTION_PATH = 'data/intercap/sequences_canonical'\n", "OBJECT_PATH = '/data-local/dingbang/phys_hoi_recon/InterAct/data/intercap/objects'\n", "OBJECT_BPS_PATH = '/data-local/dingbang/phys_hoi_recon/InterAct/data/intercap/objects_bps'\n", "name = 'Sub01_Object01_Seg_0_suitcase'\n", "obj_name = 'suitcase'\n", "\n", "    "]}, {"cell_type": "code", "execution_count": 2, "id": "b4801572", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "               Variable Information               \n", "==================================================\n", "Poses shape: (321, 90)\n", "Betas shape: (10,)\n", "Trans shape: (321, 3)\n", "Gender: male\n", "Object angles shape: (321, 3)\n", "Object translation shape: (321, 3)\n", "Object name: suitcase\n", "==================================================\n"]}], "source": ["# Load human raw data\n", "with np.load(os.path.join(MOTION_PATH, name, 'human.npz'), allow_pickle=True) as f:\n", "    poses = f['poses']                # smplh(or smplx) poses\n", "    betas = f['betas']                # body shape\n", "    trans = f['trans']                # global body position\n", "    gender = str(f['gender'])         # gender\n", "\n", "# Load object raw data\n", "with np.load(os.path.join(MOTION_PATH, name, 'object.npz'), allow_pickle=True) as f:\n", "    obj_angles = f['angles']          # rotation for rigid object, axis angle\n", "    obj_trans = f['trans']            # translation for rigid object\n", "    obj_name = str(f['name'])         # object name\n", "\n", "\n", "print(\"=\" * 50)\n", "print(\"Variable Information\".center(50))\n", "print(\"=\" * 50)\n", "print(f\"Poses shape: {poses.shape}\")\n", "print(f\"Betas shape: {betas.shape}\")\n", "print(f\"Trans shape: {trans.shape}\")\n", "print(f\"Gender: {gender}\")\n", "print(f\"Object angles shape: {obj_angles.shape}\")\n", "print(f\"Object translation shape: {obj_trans.shape}\")\n", "print(f\"Object name: {obj_name}\")\n", "print(\"=\" * 50)\n"]}, {"cell_type": "code", "execution_count": 12, "id": "36596d1e", "metadata": {}, "outputs": [{"data": {"text/plain": ["['A person picks up a baseball with right hand, holds it with left hand, and touches their head with their right hand.#A/DET person/NOUN pick/VERB up/ADP a/DET baseball/NOUN with/ADP right/ADJ hand/NOUN hold/VERB it/PRON with/ADP left/ADJ hand/NOUN and/CCONJ touch/VERB their/PRON head/NOUN with/ADP their/PRON right/ADJ hand/NOUN#0.0#0.0\\n',\n", " 'An individual grasps a baseball with their right hand, secures it with their left, and then touches their head with the right hand.#An/DET individual/NOUN grasp/VERB a/DET baseball/NOUN with/ADP their/PRON right/ADJ hand/NOUN secure/VERB it/PRON with/ADP their/PRON left/ADJ and/CCONJ then/ADV touch/VERB their/PRON head/NOUN with/ADP the/DET right/ADJ hand/NOUN#0.0#0.0\\n',\n", " 'A person picks up a baseball with their right hand, holds it with their left, and touches their head with the right.#A/DET person/NOUN pick/VERB up/ADP a/DET baseball/NOUN with/ADP their/PRON right/ADJ hand/NOUN hold/VERB it/PRON with/ADP their/PRON left/ADJ and/CCONJ touch/VERB their/PRON head/NOUN with/ADP the/DET right/NOUN#0.0#0.0']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the text annotations\n", "with open(os.path.join(MOTION_PATH, name, 'text.txt'), 'r') as f:\n", "    texts = f.readlines()             # Read all the texts for this sequence\n", "texts"]}, {"cell_type": "code", "execution_count": 19, "id": "651aa204", "metadata": {}, "outputs": [{"data": {"text/plain": ["['A person move the chair.#A/DET person/NOUN move/VERB the/DET chair/NOUN#0.0#0.0']"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load the action annotations\n", "with open(os.path.join(MOTION_PATH, name, 'action.txt'), 'r') as f:\n", "    action = f.readlines()             # Read the action for this sequence\n", "action"]}, {"cell_type": "code", "execution_count": 33, "id": "baf70703", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "               Variable Information               \n", "==================================================\n", "Markers shape: (190, 77, 3)\n", "Joints shape: (190, 52, 3)\n", "==================================================\n"]}], "source": ["# Load the human joints and markers\n", "human_markers = np.load(os.path.join(MOTION_PATH, name, 'markers.npy'))\n", "human_joints = np.load(os.path.join(MOTION_PATH, name, 'joints.npy'))\n", "print(\"=\" * 50)\n", "print(\"Variable Information\".center(50))\n", "print(\"=\" * 50)\n", "print(\"Markers shape:\",human_markers.shape)\n", "print(\"Joints shape:\",human_joints.shape)\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": 3, "id": "4ede6519", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "               Variable Information               \n", "==================================================\n", "Object sampled verts: (340, 3)\n", "Object verts: (591, 3)\n", "Object faces: (1188, 3)\n", "Object BPS: (1, 1024, 3)\n", "==================================================\n"]}], "source": ["object_sample_verts = np.load(os.path.join(OBJECT_PATH, obj_name, 'sample_points.npy'))\n", "MESH = trimesh.load(os.path.join(OBJECT_PATH, obj_name, f'{obj_name}.obj'))\n", "object_verts = MESH.vertices\n", "object_faces = MESH.faces\n", "object_bps = np.load(os.path.join(OBJECT_BPS_PATH, obj_name, f'{obj_name}_1024.npy'))\n", "\n", "\n", "print(\"=\" * 50)\n", "print(\"Variable Information\".center(50))\n", "print(\"=\" * 50)\n", "print('Object sampled verts:',object_sample_verts.shape )\n", "print('Object verts:', object_verts.shape )\n", "print('Object faces:', object_faces.shape )\n", "print('Object BPS:', object_bps.shape )\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": 4, "id": "489e08a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "               Variable Information               \n", "==================================================\n", "Representation shape: (321, 962)\n", "==================================================\n"]}], "source": ["# Load the representation\n", "motion = np.load(os.path.join(MOTION_PATH, name, 'motion.npy'))\n", "print(\"=\" * 50)\n", "print(\"Variable Information\".center(50))\n", "print(\"=\" * 50)\n", "print(\"Representation shape:\", motion.shape)\n", "print(\"=\" * 50)"]}, {"cell_type": "markdown", "id": "fdeb0423", "metadata": {}, "source": ["\n", "\n", "The `motion.npy` file contains motion sequence data in the shape of `(N, D)`, where:\n", "\n", "- **`N`**: Represents the number of frames in the sequence.\n", "- **`D`**: Denotes the feature dimension, which is **962** in total.   \n", "\n", "This dimension is comprised of two parts:\n", "\n", "- Human motion (476 features): \n", "  - Marker positions (231 features): 73 markers, each with 3 spatial coordinates.\n", "  - Marker velocities (231 features): The velocity of each of the 73 markers.\n", "  - Ground contact (14 features): Information related to the subject's contact with the ground.\n", "- Object motion (486 features): \n", "  - Translation and 6D rotation (9 features): Representing the object's position and orientation.\n", "  - Velocity (9 features): The translational and rotational velocity of the object.\n", "  - Relative representation (468 features): 78 relative representations(77 + 1 ground marker) , each with 6 components."]}, {"cell_type": "code", "execution_count": 6, "id": "2eaed021", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "               Variable Information               \n", "==================================================\n", "Markers shape: (321, 77, 3)\n", "Markers velocity shape: (321, 77, 3)\n", "Marker foot contact shape: (321, 14)\n", "Object rotation (6D) shape: (321, 6)\n", "Object translation shape: (321, 3)\n", "Object angular velocity (6D) shape: (321, 6)\n", "Object velocity shape: (321, 3)\n", "Object-to-marker vectors shape: (321, 77, 3)\n", "Object-to-ground vector shape: (321, 1, 3)\n", "Marker-corresponding canonical object vertices shape: (321, 77, 3)\n", "Ground-corresponding canonical object vertex shape: (321, 1, 3)\n", "==================================================\n"]}], "source": ["# Reshape and extract variables from the 'motion' array\n", "\n", "# Markers positions:\n", "# Reshaped into (-1, 77, 3) meaning there are 77 markers with 3 coordinates each.\n", "markers = motion[:, :231].reshape(-1, 77, 3)\n", "\n", "# Markers velocities:\n", "# Reshaped into (-1, 77, 3) corresponding to 77 markers each having a 3D velocity vector.\n", "markers_velocity = motion[:, 231:462].reshape(-1, 77, 3)\n", "\n", "# Marker foot contact:\n", "# Contains 14 values indicating foot contact statuses for markers.\n", "marker_foot_contact = motion[:, 462:462+14]\n", "\n", "# Object rotation (6D):\n", "# Represents object rotation using a 6D continuous representation.\n", "object_rotation_6d = motion[:, 476:476+6]\n", "\n", "# Object translation:\n", "# Contains the object's 3D translation values.\n", "object_trans = motion[:, 482:485]\n", "\n", "# Object angular velocity (6D):\n", "# Represents the object's angular velocity in a 6-dimensional format.\n", "object_ang_vel_6d = motion[:, 485:491]\n", "\n", "# Object velocity:\n", "# Contains the object's 3D linear velocity.\n", "object_vel = motion[:, 491:494]\n", "\n", "# Vectors from the object to markers:\n", "# Reshaped into (-1, 77, 3) yielding a 3D vector for each of the 77 markers.\n", "object2marker = motion[:, 494:494+77*3].reshape(-1, 77, 3)\n", "\n", "# Vector from the object to the ground:\n", "# Reshaped into (-1, 1, 3) representing the 3D vector for ground contact.\n", "object2ground = motion[:, 725:725+3].reshape(-1, 1, 3)\n", "\n", "# Canonical Object vertices corresponding to each marker:\n", "# Reshaped into (-1, 77, 3) where each marker corresponds to one vertex (with 3 coordinates).\n", "marker_correspond_canonical_object_verts = motion[:, 728:728+77*3].reshape(-1, 77, 3)\n", "\n", "# Canonical Object vertex corresponding to the ground contact,:\n", "# Reshaped into (-1, 1, 3) for the ground-related object vertex.\n", "ground_correspond_canonical_object_verts = motion[:, 959:959+3].reshape(-1, 1, 3)\n", "\n", "print(\"=\" * 50)\n", "print(\"Variable Information\".center(50))\n", "print(\"=\" * 50)\n", "print(\"Markers shape:\", markers.shape)\n", "print(\"Markers velocity shape:\", markers_velocity.shape)\n", "print(\"Marker foot contact shape:\", marker_foot_contact.shape)\n", "print(\"Object rotation (6D) shape:\", object_rotation_6d.shape)\n", "print(\"Object translation shape:\", object_trans.shape)\n", "print(\"Object angular velocity (6D) shape:\", object_ang_vel_6d.shape)\n", "print(\"Object velocity shape:\", object_vel.shape)\n", "print(\"Object-to-marker vectors shape:\", object2marker.shape)\n", "print(\"Object-to-ground vector shape:\", object2ground.shape)\n", "print(\"Marker-corresponding canonical object vertices shape:\", marker_correspond_canonical_object_verts.shape)\n", "print(\"Ground-corresponding canonical object vertex shape:\", ground_correspond_canonical_object_verts.shape)\n", "print(\"=\" * 50)"]}], "metadata": {"kernelspec": {"display_name": "interact", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}