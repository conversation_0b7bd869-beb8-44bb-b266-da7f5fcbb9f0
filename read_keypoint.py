def save_skeleton_to_obj(hoi_data, frame_idx, save_path):
    """
    根据hoi_data提取骨架顶点，并导出为obj文件（点云形式，无面）

    参数：
        hoi_data: np.ndarray, shape (N, 591)
        frame_idx: int, 导出第几帧的骨架点
        save_path: str, 保存obj文件路径

    说明：
        从hoi_data[:, 162:318]提取52个关节坐标（x,y,z）
    """
    import numpy as np

    # 骨架点坐标
    body_pos_flat = hoi_data[frame_idx, 162:318]  # (156,)
    body_pos = body_pos_flat.reshape(52, 3)       # (52, 3)

    with open(save_path, 'w') as f:
        f.write("# Skeleton joints point cloud\n")
        for v in body_pos:
            f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")

    print(f"Saved skeleton points of frame {frame_idx} to {save_path}")
import torch
import numpy as np
hoi_data = torch.load('/data-local/dingbang/phys_hoi_recon/InterMimic/InterAct/OMOMO/sub8_smallbox_002.pt').detach().numpy()
save_skeleton_to_obj(hoi_data,100,'key.obj')