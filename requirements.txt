numpy
torch
dataclasses>=0.6
pytorch3d
matplotlib==3.4.3
spacy==3.7.4
PyYAML
click
tqdm
blobfile
trimesh
filelock
beautifulsoup4
scipy
Pillow
imageio
imageio-ffmpeg
pyrender
local-attention
smplx
plyfile
configer
boto3
torchgeometry
rtree
protobuf
open3d
setuptools
pyopengl
scikit-image
dotmap

git+https://github.com/nghorbani/human_body_prior.git
git+https://github.com/otaheri/chamfer_distance
git+https://github.com/otaheri/bps_torch