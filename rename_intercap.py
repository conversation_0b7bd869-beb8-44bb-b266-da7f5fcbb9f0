import re
import os
import shutil
from pathlib import Path

# Paths
SRC_ROOT = Path("data/intercap/sequences_canonical")
DST_ROOT = Path("data/intercap/sequences_canonical_rename")

# Make sure destination root exists
DST_ROOT.mkdir(parents=True, exist_ok=True)

# Regex to parse names like "Sub01_Object01_Seg_0_suitcase"
pattern = re.compile(r"^Sub(\d+)_Object\d+_Seg_(\d+)_(.+)$", re.IGNORECASE)

for src_folder in SRC_ROOT.iterdir():
    if not src_folder.is_dir():
        continue

    m = pattern.match(src_folder.name)
    if not m:
        print(f"Skipping unrecognized folder name: {src_folder.name}")
        continue

    subject_num, seg_num, obj_name = m.groups()
    # build new name
    new_name = f"sub{int(subject_num):02d}_{obj_name.lower()}_{int(seg_num):03d}"
    dst_folder = DST_ROOT / new_name

    # if target exists, skip or remove—here we'll overwrite
    if dst_folder.exists():
        print(f"Destination already exists, removing: {dst_folder}")
        shutil.rmtree(dst_folder)

    # copy the folder tree
    print(f"Copying:\n  {src_folder}  ->  {dst_folder}")
    shutil.copytree(src_folder, dst_folder)

print("Done.")
