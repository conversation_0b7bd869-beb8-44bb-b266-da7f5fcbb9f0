import os
import shutil
#move all the files wit 'obj in /data-local/dingbang/phys_hoi_recon/InterAct/data/grab/raw/tools/objects to /data-local/dingbang/phys_hoi_recon/InterAct/data/grab/raw/tools/object_meshes2
source_dir = '/data-local/dingbang/phys_hoi_recon/InterAct/data/grab/raw/objects'
target_dir = '/data-local/dingbang/phys_hoi_recon/InterAct/data/grab/raw/tools/object_meshes'
if not os.path.exists(target_dir):
    os.makedirs(target_dir)
    # use os.waqlk to iterate through the files in the source directory
for root, dirs, files in os.walk(source_dir):
    for file in files:
        if file.endswith('.obj'):
            source_file = os.path.join(root, file)
            target_file = os.path.join(target_dir, file)
            print(f"Moving {source_file} to {target_file}")
            shutil.copy(source_file, target_file)