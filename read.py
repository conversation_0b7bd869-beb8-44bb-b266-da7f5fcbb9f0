import numpy as np
data = np.load('/data-local/dingbang/phys_hoi_recon/InterAct/data/intercap/sequences_canonical/Sub01_Object05_Seg_0_racket/human.npz')
data2 = np.load('/data-local/dingbang/phys_hoi_recon/InterAct/data/intercap/sequences_canonical/Sub08_Object04_Seg_0_umbrella/motion.npy')

# List keys to see what arrays are stored
print("Human keys:", data.files)
# print("Object keys:", data2.files)

# Print shapes of all arrays in each file
for key in data.files:
	print(f"data[{key}].shape:", data[key])
# for key in data2.files:
# 	print(f"data2[{key}].shape:", data2[key].shape)
# print(data['angles'].shape)
# print(data2.shape)