import os
import os.path
import numpy as np
import torch
import smplx
from scipy.spatial.transform import Rotation
import pickle
import trimesh
import torch

RAW_PATH = './data/intercap/raw'
MOTION_PATH = './data/intercap/sequences'
OBJECT_PATH = './data/intercap/objects'
MODEL_PATH = './models'

# ### Objects
# * 01 suitcase
# * 02 skateboard
# * 03 soccerball
# * 04 umbrella
# * 05 racket
# * 06 toolbox
# * 07 chair
# * 08 fantabottle
# * 09 cup
# * 10 stool

# * male: 01 02 04 05 10 
# * female: 03 06 07 08 09

obj_dic = {
    '01': 'suitcase',
    '02': 'skateboard',
    '03': 'soccerball',
    '04': 'umbrella',
    '05': 'racket',
    '06': 'toolbox',
    '07': 'chair',
    '08': 'fantabottle',
    '09': 'cup',
    '10': 'stool'
}

gender_dic = {
    '01': 'male',
    '02': 'male',
    '03': 'female',
    '04': 'male',
    '05': 'male',
    '06': 'female',
    '07': 'female',
    '08': 'female',
    '09': 'female',
    '10': 'male'
}


# Arguments
# Sub_id = '10'
# gender = 'female'
# Obj_id = '05'
# obj_name = 'racket'

smpl_model_male = smplx.create(MODEL_PATH, model_type='smplx',
                          gender="male",
                          num_pca_comps=12,
                          ext='pkl')

smpl_model_female = smplx.create(MODEL_PATH, model_type='smplx',
                          gender="female",
                          num_pca_comps=12,
                         
                          ext='pkl')


smpl_models = {
    'male': smpl_model_male,
    'female': smpl_model_female
}

smpl_model_male_no_pca = smplx.create(MODEL_PATH, model_type='smplx',gender='male',use_pca=False,ext='pkl',flat_hand_mean=True,return_full_pose=True)
smpl_model_female_no_pca = smplx.create(MODEL_PATH, model_type='smplx',gender='female',use_pca=False,ext='pkl',flat_hand_mean=True,return_full_pose=True)
smpl_models_no_pca = {
    'male': smpl_model_male_no_pca,
    'female': smpl_model_female_no_pca}

def process(human, obj):
    poses, betas, trans, gender = human['poses'], human['betas'], human['trans'], str(human['gender'])
    obj_angles, obj_trans, obj_name = obj['angles'], obj['trans'], str(obj['name'])
    frame_times = poses.shape[0]
    smpl_model = smpl_models[gender]
    smpl_model_no_pca = smpl_models_no_pca[gender]
    l_coefs = poses[:, 66:78]   # (batch, 12)
    r_coefs = poses[:, 78:90]   # (batch, 12)
    l_full = torch.einsum('bi,ij->bj', [torch.from_numpy(l_coefs), smpl_model.left_hand_components])
    l_full = l_full + smpl_model.left_hand_mean

    r_full = torch.einsum('bi,ij->bj', [torch.from_numpy(r_coefs), smpl_model.right_hand_components])
    r_full = r_full + smpl_model.right_hand_mean
    print(smpl_model.left_hand_components.shape, smpl_model.left_hand_mean.shape)
    print(smpl_model.right_hand_components.shape, smpl_model.right_hand_mean.shape)

    smplx_output = smpl_model(body_pose=torch.from_numpy(poses[:, 3:66]).float(),
                              global_orient=torch.from_numpy(poses[:, :3]).float(),
                              left_hand_pose=torch.from_numpy(poses[:, 66:78]).float(),
                              right_hand_pose=torch.from_numpy(poses[:, 78:90]).float(),
                              jaw_pose=torch.zeros(frame_times, 3).float(),
                              leye_pose=torch.zeros(frame_times, 3).float(),
                              reye_pose=torch.zeros(frame_times, 3).float(),
                              expression=torch.zeros(frame_times, 10).float(),
                              betas=torch.from_numpy(betas).float(),
                              transl=torch.from_numpy(trans).float(),)
    pelvis = smplx_output.joints.detach().numpy()[:, 0, :]


    rotvecs = poses[:, :3]
    rotations = Rotation.from_rotvec(rotvecs)
    # camera extrinsic
    rotation_matrix = Rotation.from_euler('z', np.pi, degrees=False)
    # Apply the rotation to the batch of rotations
    rotated_rotations = rotation_matrix * rotations
    # Convert the rotated rotations back to rotation vectors
    poses[:, :3] = rotated_rotations.as_rotvec()

    trans = rotation_matrix.apply(trans)

    rotations2 = Rotation.from_rotvec(obj_angles)

    # Apply the rotation to the batch of rotations
    rotated_rotations2 = rotation_matrix * rotations2
    # Convert the rotated rotations back to rotation vectors
    obj_angles = rotated_rotations2.as_rotvec()
    obj_trans_delta = rotation_matrix.apply(obj_trans - pelvis)
    smplx_output = smpl_model(body_pose=torch.from_numpy(poses[:, 3:66]).float(),
                              global_orient=torch.from_numpy(poses[:, :3]).float(),
                              left_hand_pose=torch.from_numpy(poses[:, 66:78]).float(),
                              right_hand_pose=torch.from_numpy(poses[:, 78:90]).float(),
                              jaw_pose=torch.zeros(frame_times, 3).float(),
                              leye_pose=torch.zeros(frame_times, 3).float(),
                              reye_pose=torch.zeros(frame_times, 3).float(),
                              expression=torch.zeros(frame_times, 10).float(),
                              betas=torch.from_numpy(betas).float(),
                              transl=torch.from_numpy(trans).float(),)
    
    print(torch.from_numpy(poses[:, 66:78]).shape,torch.from_numpy(poses[:, 78:90]).shape,l_full.shape,r_full.shape,22222)
    smpl_model_no_pca_output = smpl_model_no_pca(body_pose=torch.from_numpy(poses[:, 3:66]).float(),
                                                 global_orient=torch.from_numpy(poses[:, :3]).float(),
                                                 left_hand_pose=l_full.float(),
                                                 right_hand_pose=r_full.float(),
                                                 jaw_pose=torch.zeros(frame_times, 3).float(),
                                                 leye_pose=torch.zeros(frame_times, 3).float(),
                                                 reye_pose=torch.zeros(frame_times, 3).float(),
                                                 expression=torch.zeros(frame_times, 10).float(),
                                                 betas=torch.from_numpy(betas).float(),
                                                 transl=torch.from_numpy(trans).float())
    
    verts = smplx_output.vertices.detach().numpy()
    pelvis = smplx_output.joints.detach().numpy()[:, 0, :]
    faces = smpl_model.faces
    # Save the human mesh as a obj file
    i=100
    if verts.shape[0] < 101:
        i=0
    human_mesh = trimesh.Trimesh(vertices=verts[i], faces=faces)
    human_mesh_no_pca = trimesh.Trimesh(vertices=smpl_model_no_pca_output.vertices.detach().numpy()[i], faces=smpl_model_no_pca.faces)
 
    obj_trans = pelvis + obj_trans_delta
    
    mesh_obj = trimesh.load(os.path.join(OBJECT_PATH, f"{obj_name}/{obj_name}.obj"), force='mesh')
    obj_verts, obj_faces = mesh_obj.vertices, mesh_obj.faces

    angle_matrix = Rotation.from_rotvec(obj_angles).as_matrix()
    obj_verts = mesh_obj.vertices[None, ...]
    obj_verts = np.matmul(obj_verts, np.transpose(angle_matrix, (0, 2, 1))) + obj_trans[:, None, :]

    diff = min(verts[:, :, 1].min(), obj_verts[:, :, 1].min())
    obj_trans[..., 1] -= diff
    trans[..., 1] -= diff
    object_mesh = trimesh.Trimesh(vertices=obj_verts[i], faces=obj_faces)

    obj = {
        'angles': np.array(obj_angles),
        'trans': np.array(obj_trans),
        'name': obj_name,
    }
    human = {
        'poses': np.array(poses),
        'betas': np.array(betas[0]),
        'trans': np.array(trans),
        'gender': gender,
    }
    hoi_new = convert_to_hoi_data(poses, trans, l_full, r_full, smpl_model_no_pca_output, obj)
    hoi_new = torch.from_numpy(hoi_new)

    return human, obj, human_mesh, object_mesh, human_mesh_no_pca, hoi_new

# import numpy as np
# import torch
from scipy.spatial.transform import Rotation as R
def axisangle_to_quat(exp_map: np.ndarray) -> np.ndarray:
    # exp_map: (M,3) axis-angle 向量
    q_xyzw = R.from_rotvec(exp_map).as_quat()           # returns [x,y,z,w]
    # 转成 [w,x,y,z]
    return np.concatenate([q_xyzw[:,3:4], q_xyzw[:,:3]], axis=1)

# def convert_to_hoi_data(poses, trans, l_full, r_full, smplx_output, obj):
#     N = poses.shape[0]  # 帧数
#     hoi_data = np.zeros((N, 591), dtype=np.float32)

#     # 0:3 - root position
#     hoi_data[:, 0:3] = trans

#     # 3:7 - root rotation (四元数)
#     root_rot_quat = R.from_rotvec(poses[:, :3]).as_quat()  # [x, y, z, w]
#     root_rot_quat = np.concatenate([root_rot_quat[:, 3:4], root_rot_quat[:, :3]], axis=1)  # 转为 [w, x, y, z]
#     hoi_data[:, 3:7] = root_rot_quat

#     # 7:9 - reserved
#     hoi_data[:, 7:9] = 0.

#     # 9:162 - DOF = body(63) + left_hand(45) + right_hand(45) = 153
#     dof = np.concatenate([poses[:, 3:66], l_full.numpy(), r_full.numpy()], axis=1)
#     hoi_data[:, 9:162] = dof
#     # dof_rotvec = np.concatenate([poses[:, 3:66], l_full.numpy(), r_full.numpy()], axis=1)  # [N, 159]

#     # # 2. 转换为欧拉角（假设每 3 列是一个旋转向量）
#     # # reshape to (-1, 3) for individual rotation vectors
#     # rotvecs = dof_rotvec.reshape(-1, 3)
#     # # convert to Euler angles, e.g. using 'xyz' order (you can change to 'zyx' etc. as needed)
#     # eulers = R.from_rotvec(rotvecs).as_euler('xyz', degrees=False)  # 输出为弧度
#     # # reshape back to original shape
#     # dof_euler = eulers.reshape(dof_rotvec.shape)

#     # # 3. 保存到 hoi_data
#     # hoi_data[:, 9:162] = dof_euler
#     # 162:318 - body joint positions (52 joints x 3)
#     body_pos = smplx_output.joints[:, :52, :].detach().cpu().numpy().reshape(N, -1)  # (N, 156)
#     hoi_data[:, 162:318] = body_pos

#     # 318:321 - obj_pos
#     hoi_data[:, 318:321] = obj['trans']

#     # 321:325 - obj_rot (四元数)
#     obj_rot_quat = R.from_euler('xyz', obj['angles']).as_quat()  # [x, y, z, w]
#     obj_rot_quat = np.concatenate([obj_rot_quat[:, 3:4], obj_rot_quat[:, :3]], axis=1)
#     hoi_data[:, 321:325] = obj_rot_quat

#     # 325:330 - reserved
#     hoi_data[:, 325:330] = 0.

#     # 330:331 - contact_obj (placeholder)
#     hoi_data[:, 330:331] = 1.

#     # 331:383 - contact_human (52 joints) (placeholder)
#     hoi_data[:, 331:383] = 0.
#     right_hand_indices = np.arange(36, 52)
#     hoi_data[:, 331 + right_hand_indices] = 1.
#     # 383:591 - body_rot (52 joints × 4 quaternion)
#     # 提取全身每个关节的 quaternion，包括 hands（如果 full_pose 有提供）
#     # full_quat = smplx_output.full_pose.view(N, -1, 4)[:, :52, :]  # (N, 52, 4)
#     # hoi_data[:, 383:591] = full_quat.reshape(N, -1).cpu().numpy()
    
#     full_pose_aa = np.concatenate([
#         poses[:, :3],       # global_orient
#         poses[:, 3:66],     # body_pose
#         l_full.numpy(),     # left_hand_pose (15 joints x3 =45)
#         r_full.numpy(),     # right_hand_pose (15 joints x3 =45)
#     ], axis=1)              # → (N,156)

#     # 2) 按 52 个关节切分，每个关节 3 维 axis–angle
#     #    SMPLX 的前 52 个关节正好 global+body(21) + 左手(15)+右手(15)+jaw?  
#     #    这里按你数据里有的顺序切，注意确认你用的是哪些关节。
#     aa_reshaped = full_pose_aa.reshape(-1, 3)  # (N*52, 3)

#     # 3) 转四元数
#     quat_xyzw = axisangle_to_quat(aa_reshaped) # (N*52,4)
#     quat_wxyz = quat_xyzw.reshape(N, 52, 4)    # (N,52,4)

#     # 4) 写回 hoi_data
#     hoi_data[:, 383:591] = quat_wxyz.reshape(N, -1)
#     return hoi_data

import numpy as np
from scipy.spatial.transform import Rotation as R

# SMPL-X (Z-up) -> Isaac Gym (Y-up) coordinate transform
def smplx_to_isaac_rotation(rotvec):
    # Apply rotation: -90 deg about X-axis (to convert Z-up to Y-up)
    rot = R.from_rotvec(rotvec)
    axis_conversion = R.from_euler('x', -90, degrees=True)
    new_rot = axis_conversion * rot
    return new_rot



def convert_to_hoi_data(poses, trans, l_full, r_full, smplx_output, obj):
    N = poses.shape[0]  # 帧数
    hoi_data = np.zeros((N, 591), dtype=np.float32)

    # 0:3 - root position
# Apply rotation to trans vectors as well
    axis_conversion = R.from_euler('x', -90, degrees=True)
    trans_rotated = axis_conversion.apply(trans)
    hoi_data[:, 0:3] = trans_rotated


    # 3:7 - root rotation (四元数)
    root_rot = smplx_to_isaac_rotation(poses[:, :3])
    root_rot_quat = root_rot.as_quat()  # [x, y, z, w]
    root_rot_quat = np.concatenate([root_rot_quat[:, 3:4], root_rot_quat[:, :3]], axis=1)  # [w, x, y, z]
    hoi_data[:, 3:7] = root_rot_quat

    # 7:9 - reserved
    hoi_data[:, 7:9] = 0.

    # 9:162 - DOF = body(63) + left_hand(45) + right_hand(45) = 153
    dof = np.concatenate([poses[:, 3:66], l_full.numpy(), r_full.numpy()], axis=1)
    hoi_data[:, 9:162] = dof
    # dof_rotvec = np.concatenate([poses[:, 3:66], l_full.numpy(), r_full.numpy()], axis=1)  # [N, 159]

    # # 2. 转换为欧拉角（假设每 3 列是一个旋转向量）
    # # reshape to (-1, 3) for individual rotation vectors
    # rotvecs = dof_rotvec.reshape(-1, 3)
    # # convert to Euler angles, e.g. using 'xyz' order (you can change to 'zyx' etc. as needed)
    # eulers = R.from_rotvec(rotvecs).as_euler('xyz', degrees=False)  # 输出为弧度
    # # reshape back to original shape
    # dof_euler = eulers.reshape(dof_rotvec.shape)

    # # 3. 保存到 hoi_data
    # hoi_data[:, 9:162] = dof_euler
    # 162:318 - body joint positions (52 joints x 3)
    body_pos = smplx_output.joints[:, :52, :].detach().cpu().numpy().reshape(N, -1)  # (N, 156)
    hoi_data[:, 162:318] = body_pos

    # 318:321 - obj_pos
    hoi_data[:, 318:321] = obj['trans']

    # 321:325 - obj_rot (四元数)
    obj_rot_quat = R.from_euler('xyz', obj['angles']).as_quat()  # [x, y, z, w]
    obj_rot_quat = np.concatenate([obj_rot_quat[:, 3:4], obj_rot_quat[:, :3]], axis=1)
    hoi_data[:, 321:325] = obj_rot_quat

    # 325:330 - reserved
    hoi_data[:, 325:330] = 0.

    # 330:331 - contact_obj (placeholder)
    hoi_data[:, 330:331] = 1.

    # 331:383 - contact_human (52 joints) (placeholder)
    hoi_data[:, 331:383] = 0.
    right_hand_indices = np.arange(36, 52)
    hoi_data[:, 331 + right_hand_indices] = 1.
    # 383:591 - body_rot (52 joints × 4 quaternion)
    # 提取全身每个关节的 quaternion，包括 hands（如果 full_pose 有提供）
    # full_quat = smplx_output.full_pose.view(N, -1, 4)[:, :52, :]  # (N, 52, 4)
    # hoi_data[:, 383:591] = full_quat.reshape(N, -1).cpu().numpy()
    
    full_pose_aa = np.concatenate([
        poses[:, :3],       # global_orient
        poses[:, 3:66],     # body_pose
        l_full.numpy(),     # left_hand_pose (15 joints x3 =45)
        r_full.numpy(),     # right_hand_pose (15 joints x3 =45)
    ], axis=1)              # → (N,156)

    # 2) 按 52 个关节切分，每个关节 3 维 axis–angle
    #    SMPLX 的前 52 个关节正好 global+body(21) + 左手(15)+右手(15)+jaw?  
    #    这里按你数据里有的顺序切，注意确认你用的是哪些关节。
    aa_reshaped = full_pose_aa.reshape(-1, 3)  # (N*52, 3)

    # 3) 转四元数
    quat_xyzw = axisangle_to_quat(aa_reshaped) # (N*52,4)
    quat_wxyz = quat_xyzw.reshape(N, 52, 4)    # (N,52,4)

    # 4) 写回 hoi_data
    hoi_data[:, 383:591] = quat_wxyz.reshape(N, -1)
    return hoi_data



# process sequences
for Sub_id in ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10']:
    Sub_motion_path = os.path.join(RAW_PATH, Sub_id)
    dirs = os.listdir(Sub_motion_path)
    for Obj_id in dirs:
        Obj_motion_path = os.path.join(Sub_motion_path, Obj_id)
        segs = os.listdir(Obj_motion_path)
        for Seg_id in segs:
            Seg_motion_path = os.path.join(Obj_motion_path, Seg_id)
            if Sub_id!='01' or Obj_id!='01':
                continue
            print('processing ' + Seg_motion_path)
            if os.path.exists(Seg_motion_path+'/res.pkl'):
                with open(Seg_motion_path+'/res.pkl', 'rb') as f:
                    human = pickle.load(f, encoding='utf8')
                body_pose = human['body_pose']
                global_orient = human['global_orient']
                left_hand_pose = human['left_hand_pose']
                right_hand_pose = human['right_hand_pose']
                body_pose = body_pose.reshape(-1, 63)
                poses = np.concatenate([global_orient, body_pose, left_hand_pose, right_hand_pose], axis=1)
                betas = human['betas']
                trans = human['transl']
                obj_angles = human['ob_pose']
                obj_trans = human['ob_trans']

                # process object
                Obj_path = os.path.join(OBJECT_PATH, obj_dic[Obj_id])
                if not os.path.exists(os.path.join(Obj_path, obj_dic[Obj_id]+'.obj')):
                    mesh_path = os.path.join(Seg_motion_path, 'Mesh', '00000_second_obj.ply')
                    mesh = trimesh.load(mesh_path, force='mesh') 
                    mesh.vertices -= obj_trans[0]
                    mesh.vertices = mesh.vertices @ Rotation.from_rotvec(-obj_angles[0]).as_matrix().T
                    os.makedirs(Obj_path, exist_ok=True)
                    mesh.export(os.path.join(Obj_path,obj_dic[Obj_id]+'.obj'))
                obj = {
                    'angles': np.array(obj_angles),
                    'trans': np.array(obj_trans),
                    'name': obj_dic[Obj_id],
                }
                human = {
                    'poses': np.array(poses),
                    'betas': np.array(betas),
                    'trans': np.array(trans),
                    'gender': gender_dic[Sub_id],
                }
                name = 'Sub'+ Sub_id + '_Object' + Obj_id + '_' + Seg_id + '_' + obj_dic[Obj_id]

                human, obj, human_mesh, object_mesh, human_mesh_no_pca, hoi_new = process(human, obj)
                
                os.makedirs(os.path.join(MOTION_PATH, name), exist_ok=True)
                np.savez(os.path.join(MOTION_PATH, name,'object.npz'), **obj)
                np.savez(os.path.join(MOTION_PATH, name, 'human.npz'), **human)
                human_mesh.export(os.path.join(MOTION_PATH, name, 'human.obj'))
                object_mesh.export(os.path.join(MOTION_PATH, name, 'object.obj'))
                human_mesh_no_pca.export(os.path.join(MOTION_PATH, name, 'human_no_pca_new.obj'))
                torch.save(hoi_new, os.path.join(MOTION_PATH, name, f'{name}.pt'))
                print('save ' + os.path.join(MOTION_PATH, name))
        
       