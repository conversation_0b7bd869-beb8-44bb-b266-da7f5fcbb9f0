import os
import numpy as np
import trimesh
import smplx
import torch
from scipy.spatial.transform import Rotation as R
MODEL_PATH = './models'
root_path = "/data-local/dingbang/phys_hoi_recon/InterAct/data/omomo/sequences_canonical/sub8_smallbox_002"
human_seq_path = root_path + "/human.npz"
obj_seq_path = root_path + "/object.npz"
obj_name = root_path.split("/")[-1].split("_")[-2]
obj_mesh_path = os.path.dirname(os.path.dirname(root_path)) + "/objects/" + obj_name + '/' + obj_name + ".obj"

human_data = np.load(human_seq_path)
object_data = np.load(obj_seq_path)


poses = torch.from_numpy(human_data['poses']).float()
betas = np.expand_dims(human_data['betas'], axis=0).repeat(poses.shape[0], axis=0)
betas = torch.from_numpy(betas).float()
print(111,betas.shape)
gender = human_data['gender']
if isinstance(gender, np.ndarray):
	gender = gender.item()  # Convert numpy scalar to Python string if needed
trans = torch.from_numpy(human_data['trans']).float()
frame_times = poses.shape[0]
print(poses.shape, betas.shape, trans.shape)
smpl_model = smplx.create(MODEL_PATH, model_type='smplx', gender=gender, use_pca=False, flat_hand_mean=False,num_betas = 16)
print(poses.shape,betas.shape,trans.shape)
smplx_output = smpl_model(body_pose=poses[:, 3:66],
                            global_orient=poses[:, :3],
                            left_hand_pose=poses[:, 66:66+45],
                            right_hand_pose=poses[:, 111:156],
                            jaw_pose=torch.zeros(frame_times, 3).float(),
                            leye_pose=torch.zeros(frame_times, 3).float(),
                            reye_pose=torch.zeros(frame_times, 3).float(),
                            expression=torch.zeros(frame_times, 10).float(),
                            betas=betas,
                            transl=trans)
print(smplx_output.vertices.shape)
human_verts_seq = smplx_output.vertices.detach().cpu().numpy()  # (T, N, 3)
human_faces = smpl_model.faces
import concurrent.futures

def export_human_mesh(i):
    out_dir = os.path.join(root_path, 'human_mesh_seq')
    os.makedirs(out_dir, exist_ok=True)
    human_mesh = trimesh.Trimesh(vertices=human_verts_seq[i], faces=human_faces)
    human_mesh.export(os.path.join(out_dir, f'frame_{i:04d}.obj'))

with concurrent.futures.ThreadPoolExecutor() as executor:
    executor.map(export_human_mesh, range(human_verts_seq.shape[0]))

print(obj_mesh_path)
obj_mesh = trimesh.load(obj_mesh_path)
print(obj_mesh.vertices.shape, obj_mesh.faces.shape)
trans = object_data['trans']
angles = object_data['angles']
num_frames = poses.shape[0]
num_points = obj_mesh.vertices.shape[0]
obj_mesh_seq = []
rot = R.from_rotvec(angles[:,None,:].repeat(num_points, axis=1).reshape(-1, 3))  # (T, N, 3)
object_verts = obj_mesh.vertices[None, :, :].repeat(num_frames, axis=0).reshape(-1, 3)  # (T, N, 3)
rotated_vertices = rot.apply(object_verts).reshape(num_frames, num_points, 3)  # (T, N, 3)
obj_verts_seq = rotated_vertices + trans[:, None, :]           # (T, N, 3)
obj_faces = obj_mesh.faces

def export_object_mesh(i):
    out_dir = os.path.join(root_path, 'object_mesh_seq')
    os.makedirs(out_dir, exist_ok=True)
    object_mesh = trimesh.Trimesh(vertices=obj_verts_seq[i], faces=obj_faces)
    object_mesh.export(os.path.join(out_dir, f'frame_{i:04d}.obj'))

with concurrent.futures.ThreadPoolExecutor() as executor:
    executor.map(export_object_mesh, range(obj_verts_seq.shape[0]))